"use client"

import React, { useMemo } from "react"
import { Eye, FileText } from "lucide-react"
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { useTemplateDetail } from "@/hooks/use-template-detail"
import { Spinner } from '@/components/ui/shadcn-io/spinner';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "../ui/table"
import { formatDate } from "@/lib/utils"

interface ViewTemplateModalProps {
  templateId: string | null
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function ViewTemplateModal({ templateId, open, onOpenChange }: ViewTemplateModalProps) {
  const { template, loading } = useTemplateDetail(templateId || undefined)

  // Build preview HTML keeping variables as placeholders
  const previewHtml = useMemo(() => {
    if (!template) return ""
    let html = ""
    for (const tb of template.template_blocks.sort((a, b) => a.order_position - b.order_position)) {
      let content = tb.block.html_content || ""
      // Keep variables as placeholders - don't replace them with default values
      html += `\n<!-- Bloc: ${tb.block.name} (#${tb.order_position}) -->\n` + content + "\n"
    }
    return html
  }, [template])

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader className="pb-0">
        <DialogTitle className="flex items-center gap-2">
        <Eye className="h-5 w-5" />
        {template?.name || "Vista de Plantilla"}
          </DialogTitle>
          <DialogDescription>
            {template?.description || "Detalls complets i vista prèvia de la plantilla seleccionada"}
          </DialogDescription>
        </DialogHeader>

        {loading ? (
          <div className="flex flex-col items-center justify-center py-12 space-y-4">
            <Spinner key="infinite" variant="infinite" size={48} />
            <span className="text-muted-foreground">
              Carregant detalls de la plantilla...
            </span>
          </div>
        ) : template ? (
          <div className="space-y-6">
            {/* Información general arriba */}
            <Card className="m-1 p-0">
              <CardHeader className="mt-1 pt-1">
                <CardTitle>Informació General</CardTitle>
              </CardHeader>
              <CardContent className="grid grid-cols-1 md:grid-cols-5 gap-4 pb-3">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Marca</label>
                  <p className="text-sm font-medium">{template.brand_name}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Estat</label>
                  <div className="flex gap-2 mt-1">
                    <Badge variant={template.is_active ? 'default' : 'secondary'}>
                      {template.is_active ? 'Activa' : 'Inactiva'}
                    </Badge>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Total Blocs</label>
                  <p className="text-sm font-medium">{template.total_blocks}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Creat</label>
                  <p className="text-xs text-muted-foreground">{formatDate(template.created_at)}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Actualitzat</label>
                  <p className="text-xs text-muted-foreground">{formatDate(template.updated_at)}</p>
                </div>
              </CardContent>
            </Card>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Left Panel - Template Blocks */}
              <div className="space-y-4 md:col-span-1">

                {/* Template Blocks */}
                <Card>
                  <CardHeader>
                    <CardTitle>Blocs ({template.total_blocks})</CardTitle>
                    <CardDescription>Estructura i ordre dels blocs de contingut</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="max-h-96 overflow-y-auto">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead className="w-16 text-center font-semibold">#</TableHead>
                            <TableHead className="font-semibold">Nom del Bloc</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {template.template_blocks
                            .sort((a, b) => a.order_position - b.order_position)
                            .map((tb, index) => (
                              <TableRow key={tb.id} className="hover:bg-muted/50 transition-colors">
                                <TableCell className="text-center font-mono text-sm font-medium text-muted-foreground">
                                  {tb.order_position}
                                </TableCell>
                                <TableCell className="font-medium">
                                  <div className="flex items-center gap-2">
                                    <span className="text-sm truncate max-w-[150px]" title={tb.block.name}>
                                      {tb.block.name}
                                    </span>
                                  </div>
                                </TableCell>
                              </TableRow>
                            ))}
                        </TableBody>
                      </Table>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Right Panel - Preview */}
              <div className="md:col-span-2 space-y-6">
                <div className="sticky top-6 max-h-[calc(100vh-200px)] overflow-y-auto" style={{ zIndex: 10 }}>
                  <Card>
                    <CardHeader>
                      <CardTitle>Vista Prèvia</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="border rounded-lg p-4 bg-white min-w-[320px] max-h-96 overflow-y-auto">
                        {previewHtml ? (
                          <div dangerouslySetInnerHTML={{ __html: previewHtml }} />
                        ) : (
                          <div className="text-sm text-muted-foreground">No hi ha contingut per mostrar.</div>
                        )}
                      </div>
                    </CardContent>
                  </Card>

                  {/* HTML Source */}
                  <Card className="mt-6">
                    <CardHeader>
                      <CardTitle>Codi HTML</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="max-h-60 overflow-y-auto">
                        <pre className="text-xs bg-muted p-4 rounded-lg overflow-x-auto">
                          <code>{template.template_blocks
                            .sort((a, b) => a.order_position - b.order_position)
                            .map(tb => tb.block.html_content || "")
                            .join("\n")}</code>
                        </pre>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center py-12 space-y-4">
            <div className="p-4 bg-red-50 rounded-full">
              <FileText className="h-8 w-8 text-red-500" />
            </div>
            <div className="text-center">
              <h3 className="font-medium">Plantilla no trobada</h3>
              <p className="text-sm text-muted-foreground">No s'han pogut carregar les dades de la plantilla.</p>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}

